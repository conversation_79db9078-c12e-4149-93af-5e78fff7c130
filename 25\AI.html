<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>宇宙引力模拟器</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background-color: #0b0b22;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        #controls {
            position: absolute;
            left: 20px;
            top: 20px;
            width: 220px;
            background: rgba(0,0,0,0.5);
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.3);
        }
        .control-group {
            margin-bottom: 12px;
        }
        label {
            display: inline-block;
            width: 80px;
            text-align: right;
            margin-right: 8px;
        }
        input[type="range"] {
            width: 120px;
        }
        button {
            margin-right: 8px;
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            opacity: 0.8;
        }
        #info {
            margin-top: 10px;
            font-size: 14px;
        }
        #canvas {
            display: block;
            width: 100vw;
            height: 100vh;
        }
    </style>
</head>
<body>
    <div id="controls">
        <div class="control-group">
            <label for="gravity">引力G：</label>
            <input type="range" id="gravity" min="10000" max="100000" step="1000" value="100000">
        </div>
        <div class="control-group">
            <label for="timeScale">时间倍率：</label>
            <input type="range" id="timeScale" min="0.1" max="5" step="0.1" value="1">
        </div>
        <div class="control-group">
            <label for="drag">速度因子：</label>
            <input type="range" id="drag" min="0.1" max="5" step="0.1" value="1">
        </div>
        <div class="control-group">
            <label for="tail">拖尾长度：</label>
            <input type="range" id="tail" min="100" max="500" step="10" value="300">
        </div>
        <div class="control-group">
            <label for="mass">新天体质量：</label>
            <input type="range" id="mass" min="10" max="100" step="5" value="50">
        </div>
        <div class="control-group">
            <label for="sizeScale">尺寸因子：</label>
            <input type="range" id="sizeScale" min="0.1" max="2" step="0.1" value="1">
        </div>
        <div class="control-group">
            <input type="checkbox" id="merge"> 合并碰撞
            <input type="checkbox" id="showVel"> 显示速度
        </div>
        <div class="control-group">
            <button id="pause">暂停</button>
            <button id="resetView">重置视角</button>
            <button id="addExample">示例系统</button>
            <button id="clear">清空</button>
        </div>
        <div id="info">
            天体数：<span id="bodyCount">0</span> | 
            FPS：<span id="fps">0</span>
        </div>
    </div>

    <canvas id="canvas"></canvas>

    <script>
        // 物理参数
        let G = 100000;          // 引力常数
        let timeScale = 1;       // 时间倍率
        let dragFactor = 1;      // 速度阻力因子
        let tailLength = 300;    // 拖尾长度
        let newMass = 50;        // 新天体质量
        let sizeScale = 1;       // 尺寸因子
        let mergeEnabled = false;// 合并碰撞
        let showVelocity = false;// 显示速度向量

        // 交互参数
        let mouse = { x: 0, y: 0, down: false, prevX: 0, prevY: 0 };
        let canvas, ctx;
        let view = { x: 0, y: 0, scale: 1 }; // 视角：偏移和缩放
        let draggingBody = null; // 鼠标拖动的天体

        // 天体对象
        class Celestial {
            constructor(x, y, vx, vy, mass, color) {
                this.x = x;
                this.y = y;
                this.vx = vx;
                this.vy = vy;
                this.mass = mass;
                this.color = color || `hsl(${Math.random()*360}, 70%, 60%)`;
                this.trail = []; // 拖尾轨迹
            }
            update() {
                // 应用速度
                this.x += this.vx * timeScale;
                this.y += this.vy * timeScale;

                // 拖尾记录
                this.trail.push({ x: this.x, y: this.y });
                if (this.trail.length > tailLength) this.trail.shift();

                // 速度阻力
                this.vx *= (1 - 0.001 * dragFactor);
                this.vy *= (1 - 0.001 * dragFactor);
            }
            draw() {
                // 转换到视角坐标
                let sx = (this.x - view.x) * view.scale;
                let sy = (this.y - view.y) * view.scale;
                let r = Math.sqrt(this.mass) * 0.5 * sizeScale;

                // 绘制拖尾
                ctx.beginPath();
                ctx.strokeStyle = this.color;
                ctx.lineWidth = 2 * view.scale;
                ctx.globalAlpha = 0.3;
                for (let i = 1; i < this.trail.length; i++) {
                    let tx = (this.trail[i].x - view.x) * view.scale;
                    let ty = (this.trail[i].y - view.y) * view.scale;
                    let prevTx = (this.trail[i-1].x - view.x) * view.scale;
                    let prevTy = (this.trail[i-1].y - view.y) * view.scale;
                    ctx.moveTo(prevTx, prevTy);
                    ctx.lineTo(tx, ty);
                }
                ctx.stroke();
                ctx.globalAlpha = 1;

                // 绘制天体
                ctx.beginPath();
                ctx.arc(sx, sy, r, 0, 2*Math.PI);
                ctx.fillStyle = this.color;
                ctx.fill();

                // 绘制速度向量
                if (showVelocity) {
                    ctx.beginPath();
                    ctx.moveTo(sx, sy);
                    ctx.lineTo(sx + this.vx*5*view.scale, sy + this.vy*5*view.scale);
                    ctx.strokeStyle = 'rgba(255,255,255,0.5)';
                    ctx.lineWidth = 1 * view.scale;
                    ctx.stroke();
                }
            }
        }

        // 天体列表
        let bodies = [];

        // 初始化
        function init() {
            canvas = document.getElementById('canvas');
            ctx = canvas.getContext('2d');
            resizeCanvas();
            bindEvents();
            startLoop();
        }

        // Canvas 适配窗口大小
        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }

        // 事件绑定
        function bindEvents() {
            window.addEventListener('resize', resizeCanvas);

            canvas.addEventListener('mousedown', (e) => {
                mouse.down = true;
                mouse.x = e.clientX;
                mouse.y = e.clientY;
                mouse.prevX = e.clientX;
                mouse.prevY = e.clientY;

                // 检查是否点击天体
                let pos = screenToWorld(e.clientX, e.clientY);
                draggingBody = bodies.find(b => {
                    let dx = pos.x - b.x;
                    let dy = pos.y - b.y;
                    return dx*dx + dy*dy < (Math.sqrt(b.mass)*0.5*sizeScale / view.scale)**2;
                });
            });

            canvas.addEventListener('mouseup', () => {
                mouse.down = false;
                draggingBody = null;
            });

            canvas.addEventListener('mousemove', (e) => {
                mouse.x = e.clientX;
                mouse.y = e.clientY;

                if (mouse.down) {
                    if (draggingBody) {
                        // 拖动天体
                        let pos = screenToWorld(e.clientX, e.clientY);
                        draggingBody.x = pos.x;
                        draggingBody.y = pos.y;
                    } else {
                        // 平移视角
                        let dx = (e.clientX - mouse.prevX) / view.scale;
                        let dy = (e.clientY - mouse.prevY) / view.scale;
                        view.x -= dx;
                        view.y -= dy;
                    }
                }

                mouse.prevX = e.clientX;
                mouse.prevY = e.clientY;
            });

            canvas.addEventListener('wheel', (e) => {
                // 滚轮缩放
                let factor = e.deltaY > 0 ? 0.9 : 1.1;
                let x = e.clientX;
                let y = e.clientY;
                let worldPos = screenToWorld(x, y);
                
                view.scale *= factor;
                view.x = worldPos.x - (x / view.scale);
                view.y = worldPos.y - (y / view.scale);
                e.preventDefault();
            });

            canvas.addEventListener('dblclick', (e) => {
                // 双击重置视角到点击位置
                let pos = screenToWorld(e.clientX, e.clientY);
                view.x = pos.x - canvas.width/(2*view.scale);
                view.y = pos.y - canvas.height/(2*view.scale);
            });

            // 右键添加天体（示例中用shift+左键代替右键，避免浏览器菜单）
            canvas.addEventListener('mousedown', (e) => {
                if (e.button === 2) { // 右键
                    e.preventDefault();
                    let pos = screenToWorld(e.clientX, e.clientY);
                    // 速度由拖动方向决定（这里简化为固定方向，实际可记录拖动距离）
                    let vx = 0, vy = 0;
                    // 示例：给一个随机速度
                    let speed = 0.5;
                    vx = (Math.random() - 0.5) * speed;
                    vy = (Math.random() - 0.5) * speed;
                    addBody(pos.x, pos.y, vx, vy, newMass);
                }
            });

            // 控件事件
            document.getElementById('gravity').addEventListener('input', (e) => G = +e.target.value);
            document.getElementById('timeScale').addEventListener('input', (e) => timeScale = +e.target.value);
            document.getElementById('drag').addEventListener('input', (e) => dragFactor = +e.target.value);
            document.getElementById('tail').addEventListener('input', (e) => tailLength = +e.target.value);
            document.getElementById('mass').addEventListener('input', (e) => newMass = +e.target.value);
            document.getElementById('sizeScale').addEventListener('input', (e) => sizeScale = +e.target.value);
            document.getElementById('merge').addEventListener('change', (e) => mergeEnabled = e.target.checked);
            document.getElementById('showVel').addEventListener('change', (e) => showVelocity = e.target.checked);
            document.getElementById('pause').addEventListener('click', () => timeScale = timeScale === 0 ? 1 : 0);
            document.getElementById('resetView').addEventListener('click', () => {
                view.x = 0;
                view.y = 0;
                view.scale = 1;
            });
            document.getElementById('addExample').addEventListener('click', addExampleSystem);
            document.getElementById('clear').addEventListener('click', () => bodies = []);
        }

        // 屏幕坐标转世界坐标
        function screenToWorld(sx, sy) {
            return {
                x: view.x + sx / view.scale,
                y: view.y + sy / view.scale
            };
        }

        // 添加天体
        function addBody(x, y, vx=0, vy=0, mass=newMass) {
            bodies.push(new Celestial(x, y, vx, vy, mass));
            updateInfo();
        }

        // 添加示例恒星系统
        function addExampleSystem() {
            // 中心恒星
            addBody(0, 0, 0, 0, 500);
            // 行星1
            addBody(20, 0, 0, 0.5, 50);
            // 行星2
            addBody(-15, 0, 0, -0.4, 40);
        }

        // 物理模拟循环
        function simulate() {
            let len = bodies.length;
            for (let i = 0; i < len; i++) {
                let b1 = bodies[i];
                for (let j = i+1; j < len; j++) {
                    let b2 = bodies[j];
                    let dx = b2.x - b1.x;
                    let dy = b2.y - b1.y;
                    let distSq = dx*dx + dy*dy;
                    if (distSq < 1) distSq = 1; // 防止除以0
                    let dist = Math.sqrt(distSq);
                    let force = G * b1.mass * b2.mass / distSq;
                    
                    // 引力加速度
                    let ax1 = force * dx / dist / b1.mass;
                    let ay1 = force * dy / dist / b1.mass;
                    let ax2 = -force * dx / dist / b2.mass;
                    let ay2 = -force * dy / dist / b2.mass;

                    b1.vx += ax1 * timeScale;
                    b1.vy += ay1 * timeScale;
                    b2.vx += ax2 * timeScale;
                    b2.vy += ay2 * timeScale;

                    // 合并碰撞检测
                    if (mergeEnabled && dist < (Math.sqrt(b1.mass)*0.5*sizeScale + Math.sqrt(b2.mass)*0.5*sizeScale)/view.scale) {
                        // 动量守恒合并
                        let totalMass = b1.mass + b2.mass;
                        let vx = (b1.mass*b1.vx + b2.mass*b2.vx) / totalMass;
                        let vy = (b1.mass*b1.vy + b2.mass*b2.vy) / totalMass;
                        let color = `hsl(${(parseInt(b1.color.match(/\d+/)[0]) + parseInt(b2.color.match(/\d+/)[0]))/2}, 70%, 60%)`;
                        bodies.splice(j, 1);
                        bodies.splice(i, 1);
                        addBody(b1.x, b1.y, vx, vy, totalMass, color);
                        len--; // 修正循环长度
                        j--;
                    }
                }

                // 速度阻力（简单实现）
                b1.vx *= (1 - 0.001 * dragFactor * timeScale);
                b1.vy *= (1 - 0.001 * dragFactor * timeScale);
            }
        }

        // 渲染
        function render() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            bodies.forEach(b => b.draw());
        }

        // 主循环
        let lastFrame = 0;
        let frameCount = 0;
        let fpsInterval = 1000;
        let lastFpsUpdate = 0;

        function startLoop() {
            requestAnimationFrame(loop);
        }

        function loop(timestamp) {
            requestAnimationFrame(loop);

            // 模拟
            if (timeScale > 0) {
                simulate();
            }

            // 渲染
            render();

            // FPS 计算
            frameCount++;
            if (timestamp - lastFpsUpdate > fpsInterval) {
                document.getElementById('fps').textContent = Math