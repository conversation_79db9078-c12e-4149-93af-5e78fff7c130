<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>交互式投石机模拟器</title>
  <style>
    body {
      display: flex;
      flex-direction: row;
      background-color: #1E1B2C;
      color: #fff;
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
    }

    /* 左侧场景与按钮区域样式 */
    .left-panel {
      flex: 3;
      display: flex;
      flex-direction: column;
    }

    #sceneCanvas {
      background-color: #2E3142;
      border: 1px solid #444;
      margin-bottom: 10px;
      width: 100%;
      height: 500px;
    }

    .button-group {
      display: flex;
      gap: 10px;
    }

    button {
      padding: 8px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      background-color: #4A475E;
      color: #fff;
    }

    button:hover {
      background-color: #686483;
    }

    /* 右侧参数面板样式 */
    .right-panel {
      flex: 1;
      background-color: #2E3142;
      border: 1px solid #444;
      border-radius: 4px;
      padding: 15px;
      margin-left: 20px;
    }

    .param-group {
      margin-bottom: 15px;
    }

    .param-group label {
      display: inline-block;
      width: 80px;
      margin-bottom: 5px;
    }

    .param-group input[type="range"] {
      width: calc(100% - 90px);
      vertical-align: middle;
    }

    .param-group span {
      margin-left: 5px;
    }

    .param-readout {
      margin-top: 5px;
      font-size: 0.9em;
      color: #bbb;
    }
  </style>
</head>

<body>
  <!-- 左侧场景与控制按钮 -->
  <div class="left-panel">
    <canvas id="sceneCanvas"></canvas>
    <div class="button-group">
      <button id="launchBtn">发射</button>
      <button id="resetBtn">重置</button>
      <button id="stepBtn">单步</button>
      <button id="continueBtn">继续</button>
      <button id="clearTrajBtn">清除轨迹</button>
    </div>
    <div class="hint">提示：可以在左侧调参数。暂停后可使用“单步”查看每一侧的力学量变化。</div>
  </div>

  <!-- 右侧参数与读数面板 -->
  <div class="right-panel">
    <div class="param-group">
      <label for="theta0">初始角度θ₀</label>
      <input type="range" id="theta0" min="-90" max="90" step="1" value="-20">
      <span id="theta0Val">-20°</span>
    </div>
    <div class="param-group">
      <label for="thetaRelease">释放角θᵣ</label>
      <input type="range" id="thetaRelease" min="0" max="180" step="1" value="60">
      <span id="thetaReleaseVal">60°</span>
    </div>
    <div class="param-group">
      <label for="thetaLimit">极限角θₗ</label>
      <input type="range" id="thetaLimit" min="0" max="180" step="1" value="125">
      <span id="thetaLimitVal">125°</span>
    </div>
    <div class="param-group">
      <label for="Larm">长臂Larm</label>
      <input type="range" id="Larm" min="1" max="5" step="0.1" value="2.4">
      <span id="LarmVal">2.40 m</span>
    </div>
    <div class="param-group">
      <label for="Lcw">短臂Lcw</label>
      <input type="range" id="Lcw" min="0.5" max="3" step="0.05" value="1.25">
      <span id="LcwVal">1.25 m</span>
    </div>
    <div class="param-group">
      <label for="m_cw">配重m_cw</label>
      <input type="range" id="m_cw" min="10" max="100" step="1" value="50">
      <span id="m_cwVal">50 kg</span>
    </div>
    <div class="param-group">
      <label for="armMassRatio">臂杆质量比</label>
      <input type="range" id="armMassRatio" min="0" max="1" step="0.01" value="0.1">
      <span id="armMassRatioVal">0.10</span>
    </div>
    <div class="param-group">
      <label for="m_p">投射物m_p</label>
      <input type="range" id="m_p" min="0.5" max="5" step="0.1" value="1.5">
      <span id="m_pVal">1.50 kg</span>
    </div>
    <div class="param-group">
      <label for="b">阻尼b</label>
      <input type="range" id="b" min="0" max="10" step="0.1" value="5">
      <span id="bVal">5.00 N·m·s</span>
    </div>
    <!-- 实时读数显示区域 -->
    <div class="param-group">
      <strong>实时读数</strong>
    </div>
    <div class="param-group">
      <label>时间t：</label><span id="tVal">0.000 s</span>
    </div>
    <div class="param-group">
      <label>角度θ：</label><span id="thetaVal">0.00°</span>
    </div>
    <div class="param-group">
      <label>角速度ω：</label><span id="omegaVal">0.000 rad/s</span>
    </div>
    <div class="param-group">
      <label>角加速度α：</label><span id="alphaVal">0.000 rad/s²</span>
    </div>
    <div class="param-group">
      <label>投射物x：</label><span id="pxVal">0.000 m</span>
    </div>
    <div class="param-group">
      <label>投射物y：</label><span id="pyVal">0.000 m</span>
    </div>
    <div class="param-group">
      <label>速度vx：</label><span id="vxVal">0.000 m/s</span>
    </div>
    <div class="param-group">
      <label>速度vy：</label><span id="vyVal">0.000 m/s</span>
    </div>
  </div>

  <script>
    // 物理参数与状态变量定义
    let g = 9.81; // 重力加速度
    let theta0 = -20 * (Math.PI / 180); // 初始角度（转换为弧度）
    let thetaRelease = 60 * (Math.PI / 180); // 释放角（弧度）
    let thetaLimit = 125 * (Math.PI / 180); // 极限角（弧度）
    let Larm = 2.40; // 长臂长度
    let Lcw = 1.25; // 短臂长度
    let m_cw = 50; // 配重质量
    let armMassRatio = 0.1; // 臂杆质量比，用于计算等效质量
    let m_p = 1.5; // 投射物质量
    let b = 5.0; // 阻尼系数
    // 计算臂杆等效质量（简单近似，可根据实际力学模型细化）
    let armMass = armMassRatio * (m_cw + m_p); 

    // 运动状态变量
    let t = 0; // 时间
    let theta = theta0; // 当前角度（弧度）
    let omega = 0; // 角速度（rad/s）
    let alpha = 0; // 角加速度（rad/s²）
    let isRunning = false; // 模拟是否在运行
    let isPaused = false; // 是否暂停
    let projectileReleased = false; // 投射物是否已释放
    let projectileTrajectory = []; // 投射物轨迹记录
    let sceneCtx; // canvas 2D 上下文
    let canvasWidth, canvasHeight; // 画布尺寸

    // 页面加载完成后初始化
    window.onload = function () {
      // 获取 canvas 上下文
      const canvas = document.getElementById('sceneCanvas');
      sceneCtx = canvas.getContext('2d');
      canvasWidth = canvas.width;
      canvasHeight = canvas.height;

      // 绑定参数滑块事件（实时更新变量 + 实时更新显示文本）
      bindParamSlider('theta0', -90, 90, 1, (val) => {
        theta0 = val * (Math.PI / 180);
        updateParamDisplay('theta0Val', `${val}°`);
      });
      bindParamSlider('thetaRelease', 0, 180, 1, (val) => {
        thetaRelease = val * (Math.PI / 180);
        updateParamDisplay('thetaReleaseVal', `${val}°`);
      });
      bindParamSlider('thetaLimit', 0, 180, 1, (val) => {
        thetaLimit = val * (Math.PI / 180);
        updateParamDisplay('thetaLimitVal', `${val}°`);
      });
      bindParamSlider('Larm', 1, 5, 0.1, (val) => {
        Larm = val;
        updateParamDisplay('LarmVal', `${val.toFixed(2)} m`);
      });
      bindParamSlider('Lcw', 0.5, 3, 0.05, (val) => {
        Lcw = val;
        updateParamDisplay('LcwVal', `${val.toFixed(2)} m`);
      });
      bindParamSlider('m_cw', 10, 100, 1, (val) => {
        m_cw = val;
        updateParamDisplay('m_cwVal', `${val} kg`);
      });
      bindParamSlider('armMassRatio', 0, 1, 0.01, (val) => {
        armMassRatio = val;
        armMass = armMassRatio * (m_cw + m_p); // 简单更新等效质量
        updateParamDisplay('armMassRatioVal', `${val.toFixed(2)}`);
      });
      bindParamSlider('m_p', 0.5, 5, 0.1, (val) => {
        m_p = val;
        armMass = armMassRatio * (m_cw + m_p); // 同步更新等效质量
        updateParamDisplay('m_pVal', `${val.toFixed(2)} kg`);
      });
      bindParamSlider('b', 0, 10, 0.1, (val) => {
        b = val;
        updateParamDisplay('bVal', `${val.toFixed(2)} N·m·s`);
      });

      // 绑定按钮事件
      document.getElementById('launchBtn').addEventListener('click', launch);
      document.getElementById('resetBtn').addEventListener('click', resetSim);
      document.getElementById('stepBtn').addEventListener('click', stepSim);
      document.getElementById('continueBtn').addEventListener('click', continueSim);
      document.getElementById('clearTrajBtn').addEventListener('click', clearTrajectory);

      // 初始绘制（重置后的状态）
      resetSim();
    };

    // 绑定滑块：元素ID、最小值、最大值、步长、值变化时的回调
    function bindParamSlider(id, min, max, step, onChange) {
      const slider = document.getElementById(id);
      slider.min = min;
      slider.max = max;
      slider.step = step;
      slider.addEventListener('input', () => {
        const val = parseFloat(slider.value);
        onChange(val);
        // 如果模拟未运行，直接刷新显示；若运行中，下次模拟迭代生效
        if (!isRunning) {
          updateScene();
        }
      });
    }

    // 更新右侧参数显示文本
    function updateParamDisplay(id, text) {
      document.getElementById(id).textContent = text;
    }

    // 发射按钮逻辑：启动模拟循环
    function launch() {
      isRunning = true;
      isPaused = false;
      projectileReleased = false;
      projectileTrajectory = []; // 清空之前的轨迹
      requestAnimationFrame(simLoop);
    }

    // 重置模拟
    function resetSim() {
      isRunning = false;
      isPaused = false;
      projectileReleased = false;
      t = 0;
      theta = theta0;
      omega = 0;
      alpha = 0;
      projectileTrajectory = [];
      updateScene();
      // 重置实时读数
      updateRealTimeReadouts();
    }

    // 单步执行（暂停时用）
    function stepSim() {
      if (isPaused) {
        runOneStep();
        updateScene();
      }
    }

    // 继续执行（从暂停恢复）
    function continueSim() {
      isPaused = false;
      if (isRunning) {
        requestAnimationFrame(simLoop);
      }
    }

    // 清除轨迹
    function clearTrajectory() {
      projectileTrajectory = [];
      updateScene();
    }

    // 模拟主循环
    function simLoop() {
      if (!isRunning || isPaused) return;

      runOneStep(); // 执行一次物理计算
      updateScene(); // 绘制场景

      // 判断是否到达释放角或极限角
      if (!projectileReleased && theta >= thetaRelease) {
        projectileReleased = true;
        // 记录投射物脱离时的线速度：v = ω × Larm（长臂端点速度）
        const vLaunch = Math.abs(omega) * Larm; 
        const launchAngleRad = theta; // 脱离时的角度（弧度）
        // 转换为抛体运动的初速度分量（注意坐标系：canvas 中 y 向下为正，物理上重力向下，这里需要统一）
        // canvas 坐标系：原点在左上角，x 向右，y 向下；物理上 y 向上为正，所以需要转换
        const vx0 = vLaunch * Math.cos(launchAngleRad); 
        const vy0 = -vLaunch * Math.sin(launchAngleRad); // canvas y 向下，所以物理 vy 向上为负
        // 抛体运动参数：一旦释放，单独用抛体公式更新（无空气阻力）
        // 记录脱离时间、位置、初速度
        const releaseTime = t;
        const releaseX = canvasWidth / 2 + Larm * Math.sin(theta) - Lcw * Math.sin(theta0); // 简化的初始位置计算，可根据需求精细调整
        const releaseY = canvasHeight / 2 - Larm * Math.cos(theta) + Lcw * Math.cos(theta0); 
        projectileTrajectory.push({ 
          type: 'projectile', 
          releaseTime: releaseTime, 
          x0: releaseX, 
          y0: releaseY, 
          vx0: vx0, 
          vy0: vy0 
        });
      }

      // 判断是否需要停止模拟（到达极限角或其他条件）
      if (theta >= thetaLimit) {
        isRunning = false;
        return;
      }

      requestAnimationFrame(simLoop);
    }

    // 单次模拟步骤（物理计算）
    function runOneStep() {
      const dt = 0.01; // 时间步长，可根据精度调整

      // 计算合力矩（简化模型：重力矩 - 阻尼力矩）
      // 配重的力臂：短臂长度在垂直方向的分量（角度θ对应的力臂是 Lcw * sin(theta) ，重力矩方向由角度决定）
      // 重力矩：m_cw * g * Lcw * sin(theta) （当 theta 使得配重在下方时，产生的力矩驱动杠杆旋转）